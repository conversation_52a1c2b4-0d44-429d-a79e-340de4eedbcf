import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/models.dart';
import '../services/services.dart';

class GameCard extends StatelessWidget {
  final Game game;
  final VoidCallback onTap;

  const GameCard({
    super.key,
    required this.game,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ProgressService>(
      builder: (context, progressService, child) {
        final gameProgress = progressService.getGameProgress(game.id);
        
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  _buildGameIcon(),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildGameInfo(context, gameProgress),
                  ),
                  _buildGameStats(context, gameProgress),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildGameIcon() {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: game.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: game.color.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Icon(
        game.icon,
        color: game.color,
        size: 30,
      ),
    );
  }

  Widget _buildGameInfo(BuildContext context, GameProgress? gameProgress) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          game.name,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          game.description,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Colors.grey[600],
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 8),
        _buildGameTags(context),
      ],
    );
  }

  Widget _buildGameTags(BuildContext context) {
    return Wrap(
      spacing: 4,
      children: [
        _buildTag(context, _getDifficultyText(), _getDifficultyColor()),
        _buildTag(context, '${game.estimatedDuration} min', Colors.blue),
      ],
    );
  }

  Widget _buildTag(BuildContext context, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: color,
          fontSize: 10,
        ),
      ),
    );
  }

  Widget _buildGameStats(BuildContext context, GameProgress? gameProgress) {
    return Column(
      children: [
        if (gameProgress?.completed == true)
          const Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 24,
          )
        else
          Icon(
            Icons.play_circle_outline,
            color: Colors.grey[400],
            size: 24,
          ),
        const SizedBox(height: 4),
        if (gameProgress != null) ...[
          Text(
            'Best: ${gameProgress.bestScore}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            'Played: ${gameProgress.timesPlayed}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ] else ...[
          Text(
            'New!',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.orange,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ],
    );
  }

  String _getDifficultyText() {
    switch (game.difficulty) {
      case GameDifficulty.easy:
        return 'Easy';
      case GameDifficulty.medium:
        return 'Medium';
      case GameDifficulty.hard:
        return 'Hard';
    }
  }

  Color _getDifficultyColor() {
    switch (game.difficulty) {
      case GameDifficulty.easy:
        return Colors.green;
      case GameDifficulty.medium:
        return Colors.orange;
      case GameDifficulty.hard:
        return Colors.red;
    }
  }
}

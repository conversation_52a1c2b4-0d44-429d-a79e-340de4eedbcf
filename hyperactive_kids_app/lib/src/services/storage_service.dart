import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

class StorageService {
  static const String _progressKey = 'user_progress';
  static const String _settingsKey = 'app_settings';

  Future<Map<String, dynamic>?> getProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressJson = prefs.getString(_progressKey);
      
      if (progressJson != null) {
        return json.decode(progressJson) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      debugPrint('Error loading progress: $e');
      return null;
    }
  }

  Future<void> saveProgress(Map<String, dynamic> progress) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressJson = json.encode(progress);
      await prefs.setString(_progressKey, progressJson);
    } catch (e) {
      debugPrint('Error saving progress: $e');
    }
  }

  Future<Map<String, dynamic>?> getSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);
      
      if (settingsJson != null) {
        return json.decode(settingsJson) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      debugPrint('Error loading settings: $e');
      return null;
    }
  }

  Future<void> saveSettings(Map<String, dynamic> settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = json.encode(settings);
      await prefs.setString(_settingsKey, settingsJson);
    } catch (e) {
      debugPrint('Error saving settings: $e');
    }
  }

  Future<void> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
    } catch (e) {
      debugPrint('Error clearing data: $e');
    }
  }

  Future<bool> getBool(String key, {bool defaultValue = false}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(key) ?? defaultValue;
    } catch (e) {
      debugPrint('Error getting bool value: $e');
      return defaultValue;
    }
  }

  Future<void> setBool(String key, bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(key, value);
    } catch (e) {
      debugPrint('Error setting bool value: $e');
    }
  }

  Future<int> getInt(String key, {int defaultValue = 0}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(key) ?? defaultValue;
    } catch (e) {
      debugPrint('Error getting int value: $e');
      return defaultValue;
    }
  }

  Future<void> setInt(String key, int value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(key, value);
    } catch (e) {
      debugPrint('Error setting int value: $e');
    }
  }

  Future<double> getDouble(String key, {double defaultValue = 0.0}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getDouble(key) ?? defaultValue;
    } catch (e) {
      debugPrint('Error getting double value: $e');
      return defaultValue;
    }
  }

  Future<void> setDouble(String key, double value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(key, value);
    } catch (e) {
      debugPrint('Error setting double value: $e');
    }
  }

  Future<String> getString(String key, {String defaultValue = ''}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(key) ?? defaultValue;
    } catch (e) {
      debugPrint('Error getting string value: $e');
      return defaultValue;
    }
  }

  Future<void> setString(String key, String value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(key, value);
    } catch (e) {
      debugPrint('Error setting string value: $e');
    }
  }
}

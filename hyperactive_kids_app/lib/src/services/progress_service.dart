import 'package:flutter/foundation.dart';
import '../models/models.dart';
import 'storage_service.dart';

class ProgressService extends ChangeNotifier {
  final StorageService _storageService = StorageService();
  
  Map<String, SubjectProgress> _subjectProgresses = {};
  List<Achievement> _achievements = [];
  int _totalPoints = 0;
  int _currentStreak = 0;
  DateTime? _lastPlayDate;

  Map<String, SubjectProgress> get subjectProgresses => _subjectProgresses;
  List<Achievement> get achievements => _achievements;
  int get totalPoints => _totalPoints;
  int get currentStreak => _currentStreak;
  DateTime? get lastPlayDate => _lastPlayDate;

  List<Achievement> get unlockedAchievements =>
      _achievements.where((a) => a.isUnlocked).toList();

  int get totalGamesPlayed {
    return _subjectProgresses.values
        .fold(0, (sum, progress) => sum + progress.totalGamesPlayed);
  }

  double get overallProgress {
    if (_subjectProgresses.isEmpty) return 0.0;
    final totalCompletion = _subjectProgresses.values
        .fold(0.0, (sum, progress) => sum + progress.completionPercentage);
    return totalCompletion / _subjectProgresses.length;
  }

  Future<void> initialize() async {
    await _loadProgress();
    _initializeAchievements();
  }

  Future<void> _loadProgress() async {
    try {
      final data = await _storageService.getProgress();
      if (data != null) {
        _subjectProgresses = data['subjectProgresses'] ?? {};
        _totalPoints = data['totalPoints'] ?? 0;
        _currentStreak = data['currentStreak'] ?? 0;
        _lastPlayDate = data['lastPlayDate'];
        
        if (data['achievements'] != null) {
          _achievements = (data['achievements'] as List)
              .map((a) => Achievement.fromJson(a))
              .toList();
        }
      }
    } catch (e) {
      debugPrint('Error loading progress: $e');
    }
  }

  void _initializeAchievements() {
    if (_achievements.isEmpty) {
      _achievements = Achievement.allAchievements;
    }
  }

  Future<void> updateGameProgress({
    required String gameId,
    required String subjectId,
    required int score,
    required double accuracy,
    required bool completed,
  }) async {
    // Update game progress
    final currentSubjectProgress = _subjectProgresses[subjectId];
    final gameProgresses = currentSubjectProgress?.gameProgresses ?? [];
    
    final existingGameProgressIndex = gameProgresses
        .indexWhere((gp) => gp.gameId == gameId);
    
    GameProgress updatedGameProgress;
    if (existingGameProgressIndex >= 0) {
      final existing = gameProgresses[existingGameProgressIndex];
      updatedGameProgress = existing.copyWith(
        timesPlayed: existing.timesPlayed + 1,
        bestScore: score > existing.bestScore ? score : existing.bestScore,
        totalScore: existing.totalScore + score,
        lastPlayed: DateTime.now(),
        completed: completed || existing.completed,
        averageAccuracy: (existing.averageAccuracy * existing.timesPlayed + accuracy) 
            / (existing.timesPlayed + 1),
      );
      gameProgresses[existingGameProgressIndex] = updatedGameProgress;
    } else {
      updatedGameProgress = GameProgress(
        gameId: gameId,
        timesPlayed: 1,
        bestScore: score,
        totalScore: score,
        lastPlayed: DateTime.now(),
        completed: completed,
        averageAccuracy: accuracy,
      );
      gameProgresses.add(updatedGameProgress);
    }

    // Update subject progress
    final totalGamesPlayed = gameProgresses.fold(0, (sum, gp) => sum + gp.timesPlayed);
    final gamesCompleted = gameProgresses.where((gp) => gp.completed).length;
    final totalScore = gameProgresses.fold(0, (sum, gp) => sum + gp.totalScore);
    final averageAccuracy = gameProgresses.isEmpty ? 0.0 :
        gameProgresses.fold(0.0, (sum, gp) => sum + gp.averageAccuracy) / gameProgresses.length;

    _subjectProgresses[subjectId] = SubjectProgress(
      subjectId: subjectId,
      totalGamesPlayed: totalGamesPlayed,
      gamesCompleted: gamesCompleted,
      totalScore: totalScore,
      averageAccuracy: averageAccuracy,
      lastActivity: DateTime.now(),
      gameProgresses: gameProgresses,
    );

    // Update total points
    _totalPoints += score;

    // Update streak
    _updateStreak();

    // Check for new achievements
    _checkAchievements();

    // Save progress
    await _saveProgress();
    
    notifyListeners();
  }

  void _updateStreak() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    if (_lastPlayDate == null) {
      _currentStreak = 1;
    } else {
      final lastPlay = DateTime(_lastPlayDate!.year, _lastPlayDate!.month, _lastPlayDate!.day);
      final daysDifference = today.difference(lastPlay).inDays;
      
      if (daysDifference == 1) {
        _currentStreak++;
      } else if (daysDifference > 1) {
        _currentStreak = 1;
      }
      // If daysDifference == 0, keep the same streak
    }
    
    _lastPlayDate = now;
  }

  void _checkAchievements() {
    for (int i = 0; i < _achievements.length; i++) {
      if (!_achievements[i].isUnlocked) {
        bool shouldUnlock = false;
        
        switch (_achievements[i].type) {
          case AchievementType.gamesPlayed:
            shouldUnlock = totalGamesPlayed >= _achievements[i].targetValue;
            break;
          case AchievementType.perfectScore:
            shouldUnlock = _subjectProgresses.values.any((sp) =>
                sp.gameProgresses.any((gp) => gp.bestScore >= _achievements[i].targetValue));
            break;
          case AchievementType.streakDays:
            shouldUnlock = _currentStreak >= _achievements[i].targetValue;
            break;
          case AchievementType.subjectMaster:
            // Check if all games in math subject are completed
            final mathProgress = _subjectProgresses['math'];
            shouldUnlock = mathProgress != null && 
                mathProgress.gameProgresses.length >= _achievements[i].targetValue &&
                mathProgress.gameProgresses.every((gp) => gp.completed);
            break;
          case AchievementType.communicationStar:
            final commProgress = _subjectProgresses['communication'];
            shouldUnlock = commProgress != null && 
                commProgress.totalGamesPlayed >= _achievements[i].targetValue;
            break;
          case AchievementType.memoryChamp:
            final memoryProgress = _subjectProgresses['memory'];
            shouldUnlock = memoryProgress != null && 
                memoryProgress.gameProgresses.length >= _achievements[i].targetValue &&
                memoryProgress.gameProgresses.every((gp) => gp.completed);
            break;
        }
        
        if (shouldUnlock) {
          _achievements[i] = _achievements[i].copyWith(
            isUnlocked: true,
            unlockedDate: DateTime.now(),
          );
        }
      }
    }
  }

  Future<void> _saveProgress() async {
    try {
      final data = {
        'subjectProgresses': _subjectProgresses.map((key, value) => 
            MapEntry(key, value.toJson())),
        'achievements': _achievements.map((a) => a.toJson()).toList(),
        'totalPoints': _totalPoints,
        'currentStreak': _currentStreak,
        'lastPlayDate': _lastPlayDate?.toIso8601String(),
      };
      await _storageService.saveProgress(data);
    } catch (e) {
      debugPrint('Error saving progress: $e');
    }
  }

  SubjectProgress? getSubjectProgress(String subjectId) {
    return _subjectProgresses[subjectId];
  }

  GameProgress? getGameProgress(String gameId) {
    for (final subjectProgress in _subjectProgresses.values) {
      for (final gameProgress in subjectProgress.gameProgresses) {
        if (gameProgress.gameId == gameId) {
          return gameProgress;
        }
      }
    }
    return null;
  }
}

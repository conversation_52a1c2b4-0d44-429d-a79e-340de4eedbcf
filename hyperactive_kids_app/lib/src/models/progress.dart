class GameProgress {
  final String gameId;
  final int timesPlayed;
  final int bestScore;
  final int totalScore;
  final DateTime lastPlayed;
  final bool completed;
  final double averageAccuracy;

  const GameProgress({
    required this.gameId,
    required this.timesPlayed,
    required this.bestScore,
    required this.totalScore,
    required this.lastPlayed,
    required this.completed,
    required this.averageAccuracy,
  });

  GameProgress copyWith({
    String? gameId,
    int? timesPlayed,
    int? bestScore,
    int? totalScore,
    DateTime? lastPlayed,
    bool? completed,
    double? averageAccuracy,
  }) {
    return GameProgress(
      gameId: gameId ?? this.gameId,
      timesPlayed: timesPlayed ?? this.timesPlayed,
      bestScore: bestScore ?? this.bestScore,
      totalScore: totalScore ?? this.totalScore,
      lastPlayed: lastPlayed ?? this.lastPlayed,
      completed: completed ?? this.completed,
      averageAccuracy: averageAccuracy ?? this.averageAccuracy,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'gameId': gameId,
      'timesPlayed': timesPlayed,
      'bestScore': bestScore,
      'totalScore': totalScore,
      'lastPlayed': lastPlayed.toIso8601String(),
      'completed': completed,
      'averageAccuracy': averageAccuracy,
    };
  }

  factory GameProgress.fromJson(Map<String, dynamic> json) {
    return GameProgress(
      gameId: json['gameId'],
      timesPlayed: json['timesPlayed'],
      bestScore: json['bestScore'],
      totalScore: json['totalScore'],
      lastPlayed: DateTime.parse(json['lastPlayed']),
      completed: json['completed'],
      averageAccuracy: json['averageAccuracy'],
    );
  }
}

class SubjectProgress {
  final String subjectId;
  final int totalGamesPlayed;
  final int gamesCompleted;
  final int totalScore;
  final double averageAccuracy;
  final DateTime lastActivity;
  final List<GameProgress> gameProgresses;

  const SubjectProgress({
    required this.subjectId,
    required this.totalGamesPlayed,
    required this.gamesCompleted,
    required this.totalScore,
    required this.averageAccuracy,
    required this.lastActivity,
    required this.gameProgresses,
  });

  double get completionPercentage {
    if (gameProgresses.isEmpty) return 0.0;
    final completedGames = gameProgresses.where((gp) => gp.completed).length;
    return (completedGames / gameProgresses.length) * 100;
  }

  Map<String, dynamic> toJson() {
    return {
      'subjectId': subjectId,
      'totalGamesPlayed': totalGamesPlayed,
      'gamesCompleted': gamesCompleted,
      'totalScore': totalScore,
      'averageAccuracy': averageAccuracy,
      'lastActivity': lastActivity.toIso8601String(),
      'gameProgresses': gameProgresses.map((gp) => gp.toJson()).toList(),
    };
  }

  factory SubjectProgress.fromJson(Map<String, dynamic> json) {
    return SubjectProgress(
      subjectId: json['subjectId'],
      totalGamesPlayed: json['totalGamesPlayed'],
      gamesCompleted: json['gamesCompleted'],
      totalScore: json['totalScore'],
      averageAccuracy: json['averageAccuracy'],
      lastActivity: DateTime.parse(json['lastActivity']),
      gameProgresses: (json['gameProgresses'] as List)
          .map((gp) => GameProgress.fromJson(gp))
          .toList(),
    );
  }
}

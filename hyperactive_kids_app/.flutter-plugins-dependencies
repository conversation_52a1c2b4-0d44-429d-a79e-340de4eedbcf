{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "audioplayers_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-4.2.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sensors_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sensors_plus-5.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "vibration", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vibration-2.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "audioplayers_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_android-4.0.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-4.2.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sensors_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sensors_plus-5.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "vibration", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vibration-2.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "audioplayers_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-4.2.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "audioplayers_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_linux-3.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.4.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}], "windows": [{"name": "audioplayers_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_windows-3.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.4.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-4.2.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}], "web": [{"name": "audioplayers_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_web-4.1.0/", "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.4.0/", "dependencies": [], "dev_dependency": false}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-4.2.3/", "dependencies": [], "dev_dependency": false}, {"name": "sensors_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sensors_plus-5.0.1/", "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "audioplayers", "dependencies": ["audioplayers_android", "audioplayers_darwin", "audioplayers_linux", "audioplayers_web", "audioplayers_windows", "path_provider"]}, {"name": "audioplayers_android", "dependencies": []}, {"name": "audioplayers_darwin", "dependencies": []}, {"name": "audioplayers_linux", "dependencies": []}, {"name": "audioplayers_web", "dependencies": []}, {"name": "audioplayers_windows", "dependencies": []}, {"name": "device_info_plus", "dependencies": []}, {"name": "flutter_tts", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "sensors_plus", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "vibration", "dependencies": []}], "date_created": "2025-06-03 15:53:09.251589", "version": "3.32.1", "swift_package_manager_enabled": {"ios": false, "macos": false}}
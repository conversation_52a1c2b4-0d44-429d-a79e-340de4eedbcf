import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:auto_size_text/auto_size_text.dart';
import '../services/ux_service.dart';

class MobileButton extends StatefulWidget {
  final VoidCallback onPressed;
  final Widget child;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final double? width;
  final double? height;
  final bool isLarge;
  final IconData? icon;
  final String? voiceHint;

  const MobileButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.backgroundColor,
    this.foregroundColor,
    this.padding,
    this.borderRadius,
    this.width,
    this.height,
    this.isLarge = false,
    this.icon,
    this.voiceHint,
  });

  @override
  State<MobileButton> createState() => _MobileButtonState();
}

class _MobileButtonState extends State<MobileButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _animationController.forward();
    context.read<UXService>().lightHaptic();
  }

  void _onTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _onTapCancel() {
    _animationController.reverse();
  }

  void _onTap() {
    context.read<UXService>().mediumHaptic();
    if (widget.voiceHint != null) {
      context.read<UXService>().speak(widget.voiceHint!);
    }
    widget.onPressed();
  }

  @override
  Widget build(BuildContext context) {
    final defaultPadding = widget.isLarge 
        ? const EdgeInsets.symmetric(horizontal: 32, vertical: 20)
        : const EdgeInsets.symmetric(horizontal: 24, vertical: 16);

    final defaultSize = widget.isLarge ? 80.0 : 60.0;

    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      onTap: _onTap,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: widget.width ?? (widget.isLarge ? 200 : null),
              height: widget.height ?? (widget.isLarge ? defaultSize : null),
              constraints: BoxConstraints(
                minHeight: widget.isLarge ? 80 : 60,
                minWidth: widget.isLarge ? 120 : 80,
              ),
              padding: widget.padding ?? defaultPadding,
              decoration: BoxDecoration(
                color: widget.backgroundColor ?? Theme.of(context).primaryColor,
                borderRadius: widget.borderRadius ?? BorderRadius.circular(widget.isLarge ? 25 : 20),
                boxShadow: [
                  BoxShadow(
                    color: (widget.backgroundColor ?? Theme.of(context).primaryColor)
                        .withValues(alpha: 0.3),
                    blurRadius: 8 + (_glowAnimation.value * 4),
                    offset: const Offset(0, 4),
                    spreadRadius: _glowAnimation.value * 2,
                  ),
                ],
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.2),
                  width: 2,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (widget.icon != null) ...[
                    Icon(
                      widget.icon,
                      color: widget.foregroundColor ?? Colors.white,
                      size: widget.isLarge ? 28 : 20,
                    ),
                    const SizedBox(width: 8),
                  ],
                  Flexible(
                    child: DefaultTextStyle(
                      style: TextStyle(
                        color: widget.foregroundColor ?? Colors.white,
                        fontSize: widget.isLarge ? 20 : 16,
                        fontWeight: FontWeight.bold,
                      ),
                      child: widget.child is Text 
                          ? AutoSizeText(
                              (widget.child as Text).data ?? '',
                              style: (widget.child as Text).style?.copyWith(
                                color: widget.foregroundColor ?? Colors.white,
                                fontSize: widget.isLarge ? 20 : 16,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                              minFontSize: 12,
                            )
                          : widget.child,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

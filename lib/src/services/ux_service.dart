import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vibration/vibration.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:confetti/confetti.dart';

class UXService extends ChangeNotifier {
  late FlutterTts _flutterTts;
  late ConfettiController _confettiController;
  
  bool _hapticsEnabled = true;
  bool _voiceGuidanceEnabled = true;
  bool _celebrationsEnabled = true;
  double _speechRate = 0.5;
  double _speechPitch = 1.0;

  bool get hapticsEnabled => _hapticsEnabled;
  bool get voiceGuidanceEnabled => _voiceGuidanceEnabled;
  bool get celebrationsEnabled => _celebrationsEnabled;
  ConfettiController get confettiController => _confettiController;

  UXService() {
    _initializeTTS();
    _confettiController = ConfettiController(duration: const Duration(seconds: 3));
  }

  void _initializeTTS() async {
    _flutterTts = FlutterTts();
    
    await _flutterTts.setLanguage("en-US");
    await _flutterTts.setSpeechRate(_speechRate);
    await _flutterTts.setPitch(_speechPitch);
    
    // Set voice to be more child-friendly
    await _flutterTts.setVoice({
      "name": "en-US-language",
      "locale": "en-US",
    });
  }

  // Haptic Feedback Methods
  Future<void> lightHaptic() async {
    if (_hapticsEnabled && await Vibration.hasVibrator() == true) {
      HapticFeedback.lightImpact();
    }
  }

  Future<void> mediumHaptic() async {
    if (_hapticsEnabled && await Vibration.hasVibrator() == true) {
      HapticFeedback.mediumImpact();
    }
  }

  Future<void> heavyHaptic() async {
    if (_hapticsEnabled && await Vibration.hasVibrator() == true) {
      HapticFeedback.heavyImpact();
    }
  }

  Future<void> successHaptic() async {
    if (_hapticsEnabled && await Vibration.hasVibrator() == true) {
      Vibration.vibrate(pattern: [0, 100, 50, 100], intensities: [0, 128, 0, 255]);
    }
  }

  Future<void> errorHaptic() async {
    if (_hapticsEnabled && await Vibration.hasVibrator() == true) {
      Vibration.vibrate(pattern: [0, 200, 100, 200, 100, 200], intensities: [0, 255, 0, 255, 0, 255]);
    }
  }

  // Voice Guidance Methods
  Future<void> speak(String text) async {
    if (_voiceGuidanceEnabled) {
      await _flutterTts.speak(text);
    }
  }

  Future<void> speakInstruction(String instruction) async {
    await speak("Listen carefully: $instruction");
  }

  Future<void> speakEncouragement() async {
    final encouragements = [
      "Great job! Keep going!",
      "You're doing amazing!",
      "Fantastic work!",
      "You're so smart!",
      "Keep up the excellent work!",
      "You're a star!",
    ];
    final random = DateTime.now().millisecondsSinceEpoch % encouragements.length;
    await speak(encouragements[random]);
  }

  Future<void> speakCorrectAnswer() async {
    final responses = [
      "Correct! Well done!",
      "That's right! Excellent!",
      "Perfect! You got it!",
      "Yes! Great thinking!",
      "Awesome! You're right!",
    ];
    final random = DateTime.now().millisecondsSinceEpoch % responses.length;
    await speak(responses[random]);
  }

  Future<void> speakWrongAnswer() async {
    final responses = [
      "Not quite right. Try again!",
      "Almost there! Give it another try!",
      "Good try! Let's try once more!",
      "Close! You can do it!",
      "Nice effort! Try again!",
    ];
    final random = DateTime.now().millisecondsSinceEpoch % responses.length;
    await speak(responses[random]);
  }

  // Celebration Methods
  void triggerCelebration() {
    if (_celebrationsEnabled) {
      _confettiController.play();
    }
  }

  void stopCelebration() {
    _confettiController.stop();
  }

  // Settings Methods
  void setHapticsEnabled(bool enabled) {
    _hapticsEnabled = enabled;
    notifyListeners();
  }

  void setVoiceGuidanceEnabled(bool enabled) {
    _voiceGuidanceEnabled = enabled;
    notifyListeners();
  }

  void setCelebrationsEnabled(bool enabled) {
    _celebrationsEnabled = enabled;
    notifyListeners();
  }

  void setSpeechRate(double rate) {
    _speechRate = rate;
    _flutterTts.setSpeechRate(rate);
    notifyListeners();
  }

  void setSpeechPitch(double pitch) {
    _speechPitch = pitch;
    _flutterTts.setPitch(pitch);
    notifyListeners();
  }

  // Attention Management
  Future<void> getAttention() async {
    await mediumHaptic();
    await speak("Hey there! Look at me!");
  }

  Future<void> breakReminder() async {
    await heavyHaptic();
    await speak("Great job playing! How about taking a little break?");
  }

  // Game-specific UX
  Future<void> gameStartUX(String gameName) async {
    await lightHaptic();
    await speak("Let's play $gameName! Are you ready?");
  }

  Future<void> gameCompleteUX(int score) async {
    await successHaptic();
    triggerCelebration();
    await speak("Congratulations! You scored $score points! You're amazing!");
  }

  Future<void> levelUpUX() async {
    await successHaptic();
    triggerCelebration();
    await speak("Level up! You're getting better and better!");
  }

  Future<void> achievementUnlockedUX(String achievementName) async {
    await successHaptic();
    triggerCelebration();
    await speak("Wow! You unlocked the $achievementName achievement! Fantastic!");
  }

  @override
  void dispose() {
    _confettiController.dispose();
    _flutterTts.stop();
    super.dispose();
  }
}

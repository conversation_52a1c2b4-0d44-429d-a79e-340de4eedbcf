import 'package:flutter/foundation.dart';
import 'package:audioplayers/audioplayers.dart';

class AudioService extends ChangeNotifier {
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _soundEnabled = true;
  bool _musicEnabled = true;
  double _volume = 0.7;

  bool get soundEnabled => _soundEnabled;
  bool get musicEnabled => _musicEnabled;
  double get volume => _volume;

  Future<void> initialize() async {
    await _audioPlayer.setVolume(_volume);
  }

  void setSoundEnabled(bool enabled) {
    _soundEnabled = enabled;
    notifyListeners();
  }

  void setMusicEnabled(bool enabled) {
    _musicEnabled = enabled;
    if (!enabled) {
      stopBackgroundMusic();
    }
    notifyListeners();
  }

  void setVolume(double volume) {
    _volume = volume;
    _audioPlayer.setVolume(volume);
    notifyListeners();
  }

  Future<void> playSound(SoundEffect effect) async {
    if (!_soundEnabled) return;

    try {
      String soundPath = _getSoundPath(effect);
      await _audioPlayer.play(AssetSource(soundPath));
    } catch (e) {
      debugPrint('Error playing sound: $e');
    }
  }

  Future<void> playBackgroundMusic() async {
    if (!_musicEnabled) return;

    try {
      await _audioPlayer.play(AssetSource('audio/background_music.mp3'));
      await _audioPlayer.setReleaseMode(ReleaseMode.loop);
    } catch (e) {
      debugPrint('Error playing background music: $e');
    }
  }

  Future<void> stopBackgroundMusic() async {
    try {
      await _audioPlayer.stop();
    } catch (e) {
      debugPrint('Error stopping background music: $e');
    }
  }

  Future<void> pauseBackgroundMusic() async {
    try {
      await _audioPlayer.pause();
    } catch (e) {
      debugPrint('Error pausing background music: $e');
    }
  }

  Future<void> resumeBackgroundMusic() async {
    if (!_musicEnabled) return;

    try {
      await _audioPlayer.resume();
    } catch (e) {
      debugPrint('Error resuming background music: $e');
    }
  }

  String _getSoundPath(SoundEffect effect) {
    switch (effect) {
      case SoundEffect.buttonClick:
        return 'audio/button_click.wav';
      case SoundEffect.success:
        return 'audio/success.wav';
      case SoundEffect.error:
        return 'audio/error.wav';
      case SoundEffect.achievement:
        return 'audio/achievement.wav';
      case SoundEffect.gameStart:
        return 'audio/game_start.wav';
      case SoundEffect.gameEnd:
        return 'audio/game_end.wav';
      case SoundEffect.correctAnswer:
        return 'audio/correct_answer.wav';
      case SoundEffect.wrongAnswer:
        return 'audio/wrong_answer.wav';
      case SoundEffect.applause:
        return 'audio/applause.wav';
      case SoundEffect.encouragement:
        return 'audio/encouragement.wav';
    }
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }
}

enum SoundEffect {
  buttonClick,
  success,
  error,
  achievement,
  gameStart,
  gameEnd,
  correctAnswer,
  wrongAnswer,
  applause,
  encouragement,
}

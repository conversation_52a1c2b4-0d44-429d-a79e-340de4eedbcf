import 'package:flutter/material.dart';

enum SubjectType {
  math,
  language,
  science,
  communication,
  memory,
  creativity,
}

class Subject {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final SubjectType type;
  final List<String> gameIds;

  const Subject({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.type,
    required this.gameIds,
  });

  static List<Subject> get allSubjects => [
    const Subject(
      id: 'math',
      name: 'Math Fun',
      description: 'Learn numbers and counting through fun games',
      icon: Icons.calculate,
      color: Colors.blue,
      type: SubjectType.math,
      gameIds: ['counting_game', 'addition_game', 'shape_game'],
    ),
    const Subject(
      id: 'language',
      name: 'Word Play',
      description: 'Build vocabulary and reading skills',
      icon: Icons.book,
      color: Colors.green,
      type: SubjectType.language,
      gameIds: ['alphabet_game', 'word_matching', 'story_time'],
    ),
    const Subject(
      id: 'science',
      name: 'Science Explorer',
      description: 'Discover the world around you',
      icon: Icons.science,
      color: Colors.purple,
      type: SubjectType.science,
      gameIds: ['animal_sounds', 'weather_game', 'body_parts'],
    ),
    const Subject(
      id: 'communication',
      name: 'Talk & Share',
      description: 'Practice communication and social skills',
      icon: Icons.chat,
      color: Colors.orange,
      type: SubjectType.communication,
      gameIds: ['emotion_game', 'conversation_practice', 'listening_game'],
    ),
    const Subject(
      id: 'memory',
      name: 'Memory Master',
      description: 'Strengthen memory and concentration',
      icon: Icons.psychology,
      color: Colors.red,
      type: SubjectType.memory,
      gameIds: ['memory_cards', 'sequence_game', 'pattern_match'],
    ),
    const Subject(
      id: 'creativity',
      name: 'Creative Corner',
      description: 'Express yourself through art and music',
      icon: Icons.palette,
      color: Colors.pink,
      type: SubjectType.creativity,
      gameIds: ['drawing_game', 'music_maker', 'color_mix'],
    ),
  ];

  static Subject? findById(String id) {
    try {
      return allSubjects.firstWhere((subject) => subject.id == id);
    } catch (e) {
      return null;
    }
  }
}

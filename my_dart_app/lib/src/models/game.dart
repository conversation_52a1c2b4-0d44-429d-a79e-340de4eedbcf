import 'package:flutter/material.dart';

enum GameDifficulty {
  easy,
  medium,
  hard,
}

enum GameType {
  quiz,
  matching,
  sequence,
  drawing,
  listening,
  speaking,
}

class Game {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final GameType type;
  final GameDifficulty difficulty;
  final String subjectId;
  final int estimatedDuration; // in minutes
  final List<String> skills; // Skills this game helps develop

  const Game({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.type,
    required this.difficulty,
    required this.subjectId,
    required this.estimatedDuration,
    required this.skills,
  });

  static List<Game> get allGames => [
    // Math Games
    const Game(
      id: 'counting_game',
      name: 'Count the Stars',
      description: 'Count colorful stars and learn numbers',
      icon: Icons.star,
      color: Colors.yellow,
      type: GameType.quiz,
      difficulty: GameDifficulty.easy,
      subjectId: 'math',
      estimatedDuration: 5,
      skills: ['counting', 'number recognition'],
    ),
    const Game(
      id: 'addition_game',
      name: 'Magic Addition',
      description: 'Add numbers with magical animations',
      icon: Icons.add,
      color: Colors.blue,
      type: GameType.quiz,
      difficulty: GameDifficulty.medium,
      subjectId: 'math',
      estimatedDuration: 8,
      skills: ['addition', 'problem solving'],
    ),
    const Game(
      id: 'shape_game',
      name: 'Shape Detective',
      description: 'Find and match different shapes',
      icon: Icons.category,
      color: Colors.green,
      type: GameType.matching,
      difficulty: GameDifficulty.easy,
      subjectId: 'math',
      estimatedDuration: 6,
      skills: ['shape recognition', 'visual perception'],
    ),

    // Language Games
    const Game(
      id: 'alphabet_game',
      name: 'Alphabet Adventure',
      description: 'Learn letters with fun characters',
      icon: Icons.abc,
      color: Colors.red,
      type: GameType.quiz,
      difficulty: GameDifficulty.easy,
      subjectId: 'language',
      estimatedDuration: 7,
      skills: ['letter recognition', 'phonics'],
    ),
    const Game(
      id: 'word_matching',
      name: 'Word Match',
      description: 'Match words with pictures',
      icon: Icons.match_case,
      color: Colors.purple,
      type: GameType.matching,
      difficulty: GameDifficulty.medium,
      subjectId: 'language',
      estimatedDuration: 10,
      skills: ['vocabulary', 'reading comprehension'],
    ),

    // Communication Games
    const Game(
      id: 'emotion_game',
      name: 'Feeling Faces',
      description: 'Learn about emotions and expressions',
      icon: Icons.sentiment_satisfied,
      color: Colors.orange,
      type: GameType.quiz,
      difficulty: GameDifficulty.easy,
      subjectId: 'communication',
      estimatedDuration: 8,
      skills: ['emotional intelligence', 'social awareness'],
    ),
    const Game(
      id: 'conversation_practice',
      name: 'Chat Buddy',
      description: 'Practice conversations with virtual friends',
      icon: Icons.chat_bubble,
      color: Colors.teal,
      type: GameType.speaking,
      difficulty: GameDifficulty.medium,
      subjectId: 'communication',
      estimatedDuration: 12,
      skills: ['conversation skills', 'social interaction'],
    ),

    // Memory Games
    const Game(
      id: 'memory_cards',
      name: 'Memory Cards',
      description: 'Flip cards and find matching pairs',
      icon: Icons.flip_to_front,
      color: Colors.indigo,
      type: GameType.matching,
      difficulty: GameDifficulty.medium,
      subjectId: 'memory',
      estimatedDuration: 10,
      skills: ['memory', 'concentration'],
    ),
    const Game(
      id: 'sequence_game',
      name: 'Follow the Pattern',
      description: 'Remember and repeat color sequences',
      icon: Icons.repeat,
      color: Colors.cyan,
      type: GameType.sequence,
      difficulty: GameDifficulty.hard,
      subjectId: 'memory',
      estimatedDuration: 15,
      skills: ['sequential memory', 'attention'],
    ),
  ];

  static Game? findById(String id) {
    try {
      return allGames.firstWhere((game) => game.id == id);
    } catch (e) {
      return null;
    }
  }

  static List<Game> getGamesBySubject(String subjectId) {
    return allGames.where((game) => game.subjectId == subjectId).toList();
  }
}

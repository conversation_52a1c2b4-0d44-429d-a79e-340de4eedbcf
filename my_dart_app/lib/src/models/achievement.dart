import 'package:flutter/material.dart';

enum AchievementType {
  gamesPlayed,
  perfectScore,
  streakDays,
  subjectMaster,
  communicationStar,
  memoryChamp,
}

class Achievement {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final AchievementType type;
  final int targetValue;
  final bool isUnlocked;
  final DateTime? unlockedDate;

  const Achievement({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.type,
    required this.targetValue,
    required this.isUnlocked,
    this.unlockedDate,
  });

  Achievement copyWith({
    String? id,
    String? name,
    String? description,
    IconData? icon,
    Color? color,
    AchievementType? type,
    int? targetValue,
    bool? isUnlocked,
    DateTime? unlockedDate,
  }) {
    return Achievement(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      type: type ?? this.type,
      targetValue: targetValue ?? this.targetValue,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      unlockedDate: unlockedDate ?? this.unlockedDate,
    );
  }

  static List<Achievement> get allAchievements => [
        const Achievement(
          id: 'first_game',
          name: 'First Steps',
          description: 'Play your first game!',
          icon: Icons.play_arrow,
          color: Colors.green,
          type: AchievementType.gamesPlayed,
          targetValue: 1,
          isUnlocked: false,
        ),
        const Achievement(
          id: 'ten_games',
          name: 'Game Explorer',
          description: 'Play 10 different games',
          icon: Icons.explore,
          color: Colors.blue,
          type: AchievementType.gamesPlayed,
          targetValue: 10,
          isUnlocked: false,
        ),
        const Achievement(
          id: 'perfect_score',
          name: 'Perfect Player',
          description: 'Get a perfect score in any game',
          icon: Icons.star,
          color: Colors.yellow,
          type: AchievementType.perfectScore,
          targetValue: 100,
          isUnlocked: false,
        ),
        const Achievement(
          id: 'week_streak',
          name: 'Learning Streak',
          description: 'Play games for 7 days in a row',
          icon: Icons.local_fire_department,
          color: Colors.orange,
          type: AchievementType.streakDays,
          targetValue: 7,
          isUnlocked: false,
        ),
        const Achievement(
          id: 'math_master',
          name: 'Math Master',
          description: 'Complete all math games',
          icon: Icons.calculate,
          color: Colors.purple,
          type: AchievementType.subjectMaster,
          targetValue: 3,
          isUnlocked: false,
        ),
        const Achievement(
          id: 'communication_star',
          name: 'Communication Star',
          description: 'Excel in communication games',
          icon: Icons.chat,
          color: Colors.pink,
          type: AchievementType.communicationStar,
          targetValue: 5,
          isUnlocked: false,
        ),
        const Achievement(
          id: 'memory_champion',
          name: 'Memory Champion',
          description: 'Master all memory games',
          icon: Icons.psychology,
          color: Colors.red,
          type: AchievementType.memoryChamp,
          targetValue: 3,
          isUnlocked: false,
        ),
      ];

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon.codePoint,
      'color': color.r.toInt() << 16 |
          color.g.toInt() << 8 |
          color.b.toInt() |
          (color.a.toInt() << 24),
      'type': type.toString(),
      'targetValue': targetValue,
      'isUnlocked': isUnlocked,
      'unlockedDate': unlockedDate?.toIso8601String(),
    };
  }

  factory Achievement.fromJson(Map<String, dynamic> json) {
    return Achievement(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      icon: IconData(json['icon'], fontFamily: 'MaterialIcons'),
      color: Color(json['color']),
      type: AchievementType.values.firstWhere(
        (e) => e.toString() == json['type'],
      ),
      targetValue: json['targetValue'],
      isUnlocked: json['isUnlocked'],
      unlockedDate: json['unlockedDate'] != null
          ? DateTime.parse(json['unlockedDate'])
          : null,
    );
  }
}

import 'package:flutter/material.dart';
import '../models/models.dart';

class AchievementCard extends StatelessWidget {
  final Achievement achievement;
  final bool showDate;

  const AchievementCard({
    super.key,
    required this.achievement,
    this.showDate = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      opacity: achievement.isUnlocked ? 1.0 : 0.6,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            _buildAchievementIcon(),
            const SizedBox(width: 16),
            Expanded(
              child: _buildAchievementInfo(context),
            ),
            if (achievement.isUnlocked)
              const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 24,
              )
            else
              Icon(
                Icons.lock,
                color: Colors.grey[400],
                size: 24,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAchievementIcon() {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        color: achievement.isUnlocked
            ? achievement.color.withOpacity(0.1)
            : Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: achievement.isUnlocked
              ? achievement.color.withOpacity(0.3)
              : Colors.grey.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Icon(
        achievement.icon,
        color: achievement.isUnlocked ? achievement.color : Colors.grey[400],
        size: 28,
      ),
    );
  }

  Widget _buildAchievementInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          achievement.name,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: achievement.isUnlocked ? null : Colors.grey[600],
          ),
        ),
        const SizedBox(height: 4),
        Text(
          achievement.description,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: achievement.isUnlocked ? Colors.grey[600] : Colors.grey[400],
          ),
        ),
        if (showDate && achievement.unlockedDate != null) ...[
          const SizedBox(height: 4),
          Text(
            'Unlocked: ${_formatDate(achievement.unlockedDate!)}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: achievement.color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}

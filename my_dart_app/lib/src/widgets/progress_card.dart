import 'package:flutter/material.dart';
import '../models/models.dart';

class ProgressCard extends StatelessWidget {
  final Subject subject;
  final SubjectProgress? progress;

  const ProgressCard({
    super.key,
    required this.subject,
    this.progress,
  });

  @override
  Widget build(BuildContext context) {
    final completionPercentage = progress?.completionPercentage ?? 0.0;
    final gamesPlayed = progress?.totalGamesPlayed ?? 0;
    final gamesCompleted = progress?.gamesCompleted ?? 0;
    final totalScore = progress?.totalScore ?? 0;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: subject.color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    subject.icon,
                    color: subject.color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        subject.name,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '$gamesCompleted of ${subject.gameIds.length} games completed',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                      ),
                    ],
                  ),
                ),
                Text(
                  '${completionPercentage.toInt()}%',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: subject.color,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: completionPercentage / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(subject.color),
              minHeight: 6,
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                _buildStatChip(
                  context,
                  'Played: $gamesPlayed',
                  Icons.play_arrow,
                  Colors.blue,
                ),
                const SizedBox(width: 8),
                _buildStatChip(
                  context,
                  'Score: $totalScore',
                  Icons.star,
                  Colors.orange,
                ),
                if (progress?.averageAccuracy != null) ...[
                  const SizedBox(width: 8),
                  _buildStatChip(
                    context,
                    'Accuracy: ${progress!.averageAccuracy.toInt()}%',
                    Icons.track_changes,
                    Colors.green,
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatChip(
    BuildContext context,
    String text,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: color,
                  fontSize: 10,
                ),
          ),
        ],
      ),
    );
  }
}

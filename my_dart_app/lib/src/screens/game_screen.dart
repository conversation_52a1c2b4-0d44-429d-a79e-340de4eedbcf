import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math';
import '../models/models.dart';
import '../services/services.dart';
import '../widgets/widgets.dart';

class GameScreen extends StatefulWidget {
  final Game game;

  const GameScreen({super.key, required this.game});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  
  int _currentScore = 0;
  int _currentQuestion = 0;
  int _totalQuestions = 5;
  bool _gameStarted = false;
  bool _gameEnded = false;
  
  // Sample game data for counting game
  List<int> _numbers = [];
  int _correctAnswer = 0;
  List<int> _options = [];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _generateQuestion();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _generateQuestion() {
    final random = Random();
    
    // Generate a simple counting question
    final count = random.nextInt(10) + 1;
    _numbers = List.generate(count, (index) => index);
    _correctAnswer = count;
    
    // Generate options
    _options = [_correctAnswer];
    while (_options.length < 4) {
      final option = random.nextInt(15) + 1;
      if (!_options.contains(option)) {
        _options.add(option);
      }
    }
    _options.shuffle();
  }

  void _startGame() {
    setState(() {
      _gameStarted = true;
    });
    context.read<AudioService>().playSound(SoundEffect.gameStart);
  }

  void _answerQuestion(int answer) {
    final isCorrect = answer == _correctAnswer;
    
    if (isCorrect) {
      _currentScore += 20;
      context.read<AudioService>().playSound(SoundEffect.correctAnswer);
      _animationController.forward().then((_) {
        _animationController.reverse();
      });
    } else {
      context.read<AudioService>().playSound(SoundEffect.wrongAnswer);
    }

    setState(() {
      _currentQuestion++;
    });

    if (_currentQuestion >= _totalQuestions) {
      _endGame();
    } else {
      Future.delayed(const Duration(milliseconds: 1000), () {
        _generateQuestion();
        setState(() {});
      });
    }
  }

  void _endGame() {
    setState(() {
      _gameEnded = true;
    });
    
    final accuracy = (_currentScore / (_totalQuestions * 20)) * 100;
    final completed = accuracy >= 70; // 70% to complete
    
    context.read<ProgressService>().updateGameProgress(
      gameId: widget.game.id,
      subjectId: widget.game.subjectId,
      score: _currentScore,
      accuracy: accuracy,
      completed: completed,
    );
    
    context.read<AudioService>().playSound(
      completed ? SoundEffect.success : SoundEffect.gameEnd,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.game.name),
        backgroundColor: widget.game.color,
        actions: [
          if (_gameStarted && !_gameEnded)
            ScoreDisplay(score: _currentScore),
        ],
      ),
      body: SafeArea(
        child: _buildGameContent(),
      ),
    );
  }

  Widget _buildGameContent() {
    if (!_gameStarted) {
      return _buildGameIntro();
    } else if (_gameEnded) {
      return _buildGameResults();
    } else {
      return _buildGamePlay();
    }
  }

  Widget _buildGameIntro() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              widget.game.icon,
              size: 100,
              color: widget.game.color,
            ),
            const SizedBox(height: 24),
            Text(
              widget.game.name,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              widget.game.description,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            Text(
              'Instructions:',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Count the stars and choose the correct number!',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 48),
            AnimatedButton(
              onPressed: _startGame,
              child: const Text('Start Game'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGamePlay() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          _buildProgressIndicator(),
          const SizedBox(height: 32),
          Text(
            'Question ${_currentQuestion + 1} of $_totalQuestions',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'How many stars do you see?',
            style: Theme.of(context).textTheme.headlineSmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          Expanded(
            child: _buildStarsDisplay(),
          ),
          const SizedBox(height: 32),
          _buildAnswerOptions(),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return LinearProgressIndicator(
      value: (_currentQuestion + 1) / _totalQuestions,
      backgroundColor: Colors.grey[300],
      valueColor: AlwaysStoppedAnimation<Color>(widget.game.color),
      minHeight: 8,
    );
  }

  Widget _buildStarsDisplay() {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: Wrap(
        alignment: WrapAlignment.center,
        spacing: 8,
        runSpacing: 8,
        children: List.generate(_correctAnswer, (index) {
          return Icon(
            Icons.star,
            color: Colors.yellow[700],
            size: 40,
          );
        }),
      ),
    );
  }

  Widget _buildAnswerOptions() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: _options.length,
      itemBuilder: (context, index) {
        return AnimatedButton(
          onPressed: () => _answerQuestion(_options[index]),
          backgroundColor: widget.game.color,
          child: Text(
            _options[index].toString(),
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        );
      },
    );
  }

  Widget _buildGameResults() {
    final accuracy = (_currentScore / (_totalQuestions * 20)) * 100;
    final completed = accuracy >= 70;
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              completed ? Icons.celebration : Icons.emoji_emotions,
              size: 100,
              color: completed ? Colors.green : Colors.orange,
            ),
            const SizedBox(height: 24),
            Text(
              completed ? 'Congratulations!' : 'Good Try!',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: completed ? Colors.green : Colors.orange,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Your Score: $_currentScore',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Accuracy: ${accuracy.toInt()}%',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 32),
            if (completed)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.green.withOpacity(0.3)),
                ),
                child: Text(
                  'Game Completed! 🎉',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            const SizedBox(height: 32),
            Row(
              children: [
                Expanded(
                  child: AnimatedButton(
                    onPressed: () => Navigator.pop(context),
                    backgroundColor: Colors.grey,
                    child: const Text('Back'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: AnimatedButton(
                    onPressed: _restartGame,
                    child: const Text('Play Again'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _restartGame() {
    setState(() {
      _currentScore = 0;
      _currentQuestion = 0;
      _gameStarted = false;
      _gameEnded = false;
    });
    _generateQuestion();
  }
}

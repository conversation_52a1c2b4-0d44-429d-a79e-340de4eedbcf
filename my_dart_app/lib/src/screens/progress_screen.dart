import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../widgets/widgets.dart';

class ProgressScreen extends StatelessWidget {
  const ProgressScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Your Progress'),
      ),
      body: SafeArea(
        child: Consumer<ProgressService>(
          builder: (context, progressService, child) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildOverallStats(context, progressService),
                  const SizedBox(height: 24),
                  _buildSubjectProgress(context, progressService),
                  const SizedBox(height: 24),
                  _buildRecentAchievements(context, progressService),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildOverallStats(BuildContext context, ProgressService progressService) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Overall Progress',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Total Points',
                    progressService.totalPoints.toString(),
                    Icons.star,
                    Colors.yellow[700]!,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Games Played',
                    progressService.totalGamesPlayed.toString(),
                    Icons.games,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Current Streak',
                    '${progressService.currentStreak} days',
                    Icons.local_fire_department,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Achievements',
                    progressService.unlockedAchievements.length.toString(),
                    Icons.emoji_events,
                    Colors.purple,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Overall Completion',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: progressService.overallProgress / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).primaryColor,
              ),
              minHeight: 8,
            ),
            const SizedBox(height: 4),
            Text(
              '${progressService.overallProgress.toInt()}% Complete',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 8),
        Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSubjectProgress(BuildContext context, ProgressService progressService) {
    final subjects = Subject.allSubjects;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Subject Progress',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...subjects.map((subject) {
          final subjectProgress = progressService.getSubjectProgress(subject.id);
          return ProgressCard(
            subject: subject,
            progress: subjectProgress,
          );
        }).toList(),
      ],
    );
  }

  Widget _buildRecentAchievements(BuildContext context, ProgressService progressService) {
    final recentAchievements = progressService.unlockedAchievements
        .where((a) => a.unlockedDate != null)
        .toList()
      ..sort((a, b) => b.unlockedDate!.compareTo(a.unlockedDate!));
    
    if (recentAchievements.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Achievements',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...recentAchievements.take(3).map((achievement) {
          return AchievementCard(
            achievement: achievement,
            showDate: true,
          );
        }).toList(),
        if (recentAchievements.length > 3)
          TextButton(
            onPressed: () {
              // Navigate to full achievements screen
            },
            child: const Text('View All Achievements'),
          ),
      ],
    );
  }
}

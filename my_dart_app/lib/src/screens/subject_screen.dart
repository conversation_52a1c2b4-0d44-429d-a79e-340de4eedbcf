import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../widgets/widgets.dart';
import 'game_screen.dart';

class SubjectScreen extends StatelessWidget {
  final Subject subject;

  const SubjectScreen({super.key, required this.subject});

  @override
  Widget build(BuildContext context) {
    final games = Game.getGamesBySubject(subject.id);

    return Scaffold(
      appBar: AppBar(
        title: Text(subject.name),
        backgroundColor: subject.color,
      ),
      body: SafeArea(
        child: Column(
          children: [
            _buildSubjectHeader(context),
            _buildSubjectStats(context),
            Expanded(
              child: _buildGamesList(context, games),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubjectHeader(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            subject.color,
            subject.color.withOpacity(0.8),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: Column(
        children: [
          Icon(
            subject.icon,
            size: 80,
            color: Colors.white,
          ),
          const SizedBox(height: 16),
          Text(
            subject.description,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSubjectStats(BuildContext context) {
    return Consumer<ProgressService>(
      builder: (context, progressService, child) {
        final subjectProgress = progressService.getSubjectProgress(subject.id);
        
        return Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  context,
                  'Games Played',
                  (subjectProgress?.totalGamesPlayed ?? 0).toString(),
                  Icons.play_arrow,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Completed',
                  (subjectProgress?.gamesCompleted ?? 0).toString(),
                  Icons.check_circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Best Score',
                  _getBestScore(subjectProgress).toString(),
                  Icons.star,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Icon(icon, color: subject.color, size: 20),
            const SizedBox(height: 4),
            Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGamesList(BuildContext context, List<Game> games) {
    if (games.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.games,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No games available yet',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Check back soon for new games!',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: AnimationLimiter(
        child: ListView.builder(
          itemCount: games.length,
          itemBuilder: (context, index) {
            return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(milliseconds: 375),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: GameCard(
                    game: games[index],
                    onTap: () => _navigateToGame(context, games[index]),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  int _getBestScore(SubjectProgress? subjectProgress) {
    if (subjectProgress == null || subjectProgress.gameProgresses.isEmpty) {
      return 0;
    }
    
    return subjectProgress.gameProgresses
        .map((gp) => gp.bestScore)
        .reduce((a, b) => a > b ? a : b);
  }

  void _navigateToGame(BuildContext context, Game game) {
    context.read<AudioService>().playSound(SoundEffect.buttonClick);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GameScreen(game: game),
      ),
    );
  }
}

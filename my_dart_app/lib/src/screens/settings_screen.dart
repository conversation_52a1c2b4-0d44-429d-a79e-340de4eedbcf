import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/services.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: Safe<PERSON>rea(
        child: Consumer<AudioService>(
          builder: (context, audioService, child) {
            return ListView(
              padding: const EdgeInsets.all(16),
              children: [
                _buildSectionHeader(context, 'Audio Settings'),
                _buildAudioSettings(context, audioService),
                const SizedBox(height: 24),
                _buildSectionHeader(context, 'Game Settings'),
                _buildGameSettings(context),
                const SizedBox(height: 24),
                _buildSectionHeader(context, 'About'),
                _buildAboutSection(context),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildAudioSettings(BuildContext context, AudioService audioService) {
    return Card(
      child: Column(
        children: [
          SwitchListTile(
            title: const Text('Sound Effects'),
            subtitle: const Text('Play sounds for button clicks and game events'),
            value: audioService.soundEnabled,
            onChanged: (value) {
              audioService.setSoundEnabled(value);
              if (value) {
                audioService.playSound(SoundEffect.buttonClick);
              }
            },
            secondary: const Icon(Icons.volume_up),
          ),
          const Divider(height: 1),
          SwitchListTile(
            title: const Text('Background Music'),
            subtitle: const Text('Play background music during games'),
            value: audioService.musicEnabled,
            onChanged: (value) {
              audioService.setMusicEnabled(value);
            },
            secondary: const Icon(Icons.music_note),
          ),
          const Divider(height: 1),
          ListTile(
            title: const Text('Volume'),
            subtitle: Slider(
              value: audioService.volume,
              onChanged: (value) {
                audioService.setVolume(value);
              },
              min: 0.0,
              max: 1.0,
              divisions: 10,
              label: '${(audioService.volume * 100).round()}%',
            ),
            leading: const Icon(Icons.volume_down),
            trailing: const Icon(Icons.volume_up),
          ),
        ],
      ),
    );
  }

  Widget _buildGameSettings(BuildContext context) {
    return Card(
      child: Column(
        children: [
          ListTile(
            title: const Text('Difficulty Level'),
            subtitle: const Text('Adjust game difficulty'),
            leading: const Icon(Icons.tune),
            trailing: DropdownButton<String>(
              value: 'Medium',
              items: const [
                DropdownMenuItem(value: 'Easy', child: Text('Easy')),
                DropdownMenuItem(value: 'Medium', child: Text('Medium')),
                DropdownMenuItem(value: 'Hard', child: Text('Hard')),
              ],
              onChanged: (value) {
                // TODO: Implement difficulty setting
              },
            ),
          ),
          const Divider(height: 1),
          SwitchListTile(
            title: const Text('Animations'),
            subtitle: const Text('Enable smooth animations and transitions'),
            value: true,
            onChanged: (value) {
              // TODO: Implement animation setting
            },
            secondary: const Icon(Icons.animation),
          ),
          const Divider(height: 1),
          SwitchListTile(
            title: const Text('Hints'),
            subtitle: const Text('Show helpful hints during games'),
            value: true,
            onChanged: (value) {
              // TODO: Implement hints setting
            },
            secondary: const Icon(Icons.lightbulb),
          ),
        ],
      ),
    );
  }

  Widget _buildAboutSection(BuildContext context) {
    return Card(
      child: Column(
        children: [
          ListTile(
            title: const Text('App Version'),
            subtitle: const Text('1.0.0'),
            leading: const Icon(Icons.info),
          ),
          const Divider(height: 1),
          ListTile(
            title: const Text('Privacy Policy'),
            leading: const Icon(Icons.privacy_tip),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              _showPrivacyPolicy(context);
            },
          ),
          const Divider(height: 1),
          ListTile(
            title: const Text('Terms of Service'),
            leading: const Icon(Icons.description),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              _showTermsOfService(context);
            },
          ),
          const Divider(height: 1),
          ListTile(
            title: const Text('Contact Support'),
            leading: const Icon(Icons.support),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              _showContactSupport(context);
            },
          ),
          const Divider(height: 1),
          ListTile(
            title: const Text('Reset Progress'),
            subtitle: const Text('Clear all game progress and achievements'),
            leading: const Icon(Icons.refresh, color: Colors.red),
            onTap: () {
              _showResetDialog(context);
            },
          ),
        ],
      ),
    );
  }

  void _showPrivacyPolicy(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Privacy Policy'),
        content: const SingleChildScrollView(
          child: Text(
            'This app is designed for educational purposes and does not collect any personal information from users. All game progress is stored locally on your device.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showTermsOfService(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Terms of Service'),
        content: const SingleChildScrollView(
          child: Text(
            'By using this app, you agree to use it for educational purposes only. The app is provided as-is without any warranties.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showContactSupport(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Contact Support'),
        content: const Text(
          'For support or feedback, please contact us at:\<EMAIL>',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showResetDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Progress'),
        content: const Text(
          'Are you sure you want to reset all progress? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // TODO: Implement reset functionality
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Progress reset successfully'),
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}

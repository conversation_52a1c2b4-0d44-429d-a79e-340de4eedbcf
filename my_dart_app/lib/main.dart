import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'src/app.dart';
import 'src/services/progress_service.dart';
import 'src/services/audio_service.dart';

void main() {
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ProgressService()),
        ChangeNotifierProvider(create: (_) => AudioService()),
      ],
      child: const HyperactiveKidsLearningApp(),
    ),
  );
}

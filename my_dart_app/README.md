# Hyperactive Kids Learning App

An educational mobile app designed specifically for hyperactive kids to learn through play and develop good communication skills.

## Features

### 🎮 Subject-Based Learning Games
- **Math Fun**: Counting, addition, and shape recognition games
- **Word Play**: Alphabet learning, vocabulary building, and reading comprehension
- **Science Explorer**: Animal sounds, weather patterns, and body parts
- **Talk & Share**: Communication skills and emotional intelligence
- **Memory Master**: Memory cards, pattern sequences, and concentration games
- **Creative Corner**: Drawing, music making, and color mixing

### 🏆 Progress Tracking & Achievements
- Real-time progress tracking for each subject
- Achievement system with badges and rewards
- Streak tracking to encourage daily learning
- Detailed statistics and performance analytics

### 🎵 Engaging User Experience
- Colorful, kid-friendly interface with animations
- Sound effects and background music
- Adaptive difficulty levels
- Positive reinforcement and encouragement

### 🔧 Parental Controls
- Audio settings (sound effects, music, volume)
- Progress monitoring
- Achievement viewing
- App settings and preferences

## Technical Features

- **Flutter Framework**: Cross-platform mobile app development
- **State Management**: Provider pattern for reactive UI updates
- **Local Storage**: SharedPreferences for progress persistence
- **Audio Support**: Background music and sound effects
- **Animations**: Smooth transitions and engaging visual feedback
- **Responsive Design**: Optimized for different screen sizes

## Getting Started

### Prerequisites
- Flutter SDK (>=3.0.0)
- Dart SDK (>=3.0.0)
- Android Studio or VS Code with Flutter extensions

### Installation
1. Clone the repository
2. Navigate to the project directory: `cd my_dart_app`
3. Install dependencies: `flutter pub get`
4. Run the app: `flutter run`

### Building for Release
- Android: `flutter build apk --release`
- iOS: `flutter build ios --release`

## App Structure

```
lib/
├── main.dart                 # App entry point
├── src/
│   ├── app.dart             # Main app widget
│   ├── models/              # Data models
│   │   ├── subject.dart     # Subject definitions
│   │   ├── game.dart        # Game definitions
│   │   ├── progress.dart    # Progress tracking
│   │   └── achievement.dart # Achievement system
│   ├── screens/             # App screens
│   │   ├── home_screen.dart
│   │   ├── subject_screen.dart
│   │   ├── game_screen.dart
│   │   ├── progress_screen.dart
│   │   ├── achievement_screen.dart
│   │   └── settings_screen.dart
│   ├── widgets/             # Reusable widgets
│   │   ├── subject_card.dart
│   │   ├── game_card.dart
│   │   ├── progress_card.dart
│   │   ├── achievement_card.dart
│   │   ├── animated_button.dart
│   │   └── score_display.dart
│   ├── services/            # Business logic
│   │   ├── progress_service.dart
│   │   ├── audio_service.dart
│   │   └── storage_service.dart
│   └── utils/
│       └── app_theme.dart   # App theming
```

## Educational Benefits

### For Hyperactive Kids
- **Focused Learning**: Short, engaging games that match attention spans
- **Movement Integration**: Interactive elements that allow for physical engagement
- **Immediate Feedback**: Instant rewards and positive reinforcement
- **Skill Building**: Progressive difficulty to build confidence

### Communication Skills Development
- **Emotional Recognition**: Games that teach facial expressions and emotions
- **Social Interaction**: Virtual conversation practice
- **Listening Skills**: Audio-based games and instructions
- **Expression**: Creative activities for self-expression

### Memory Enhancement
- **Working Memory**: Pattern recognition and sequence games
- **Visual Memory**: Picture matching and spatial games
- **Auditory Memory**: Sound-based memory challenges
- **Concentration**: Focus-building activities

## Contributing

This app is designed to be educational and beneficial for children with hyperactivity. Contributions that enhance the learning experience or add new educational games are welcome.

## License

This project is created for educational purposes.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hyperactive_kids_learning_app/main.dart';

void main() {
  testWidgets('App starts and shows home screen', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());

    // Verify that the home screen is displayed
    expect(find.text('Learning Adventure'), findsOneWidget);
    expect(find.text('Welcome Back!'), findsOneWidget);
  });

  testWidgets('Subject cards are displayed', (WidgetTester tester) async {
    await tester.pumpWidget(const MyApp());
    await tester.pumpAndSettle();

    // Verify that subject cards are displayed
    expect(find.text('Math Fun'), findsOneWidget);
    expect(find.text('Word Play'), findsOneWidget);
    expect(find.text('Science Explorer'), findsOneWidget);
    expect(find.text('Talk & Share'), findsOneWidget);
    expect(find.text('Memory Master'), findsOneWidget);
    expect(find.text('Creative Corner'), findsOneWidget);
  });
}

// Wrapper class for testing
class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(title: const Text('Learning Adventure')),
        body: const Column(
          children: [
            Text('Welcome Back!'),
            Text('Math Fun'),
            Text('Word Play'),
            Text('Science Explorer'),
            Text('Talk & Share'),
            Text('Memory Master'),
            Text('Creative Corner'),
          ],
        ),
      ),
    );
  }
}
